package com.bobandata.cloud.trade.elec.service.dayRolling;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveListRespVo;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.dayRolling.SpotTradeElementCurve;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service
 * @createDate 2024-03-18 10:08:06
 */
public interface SpotTradeElementCurveService extends IService<SpotTradeElementCurve> {


    ServiceResult<List<Map<String, Object>>> listRecord(SpotTradeElementCurveReqVo reqVo);

    ServiceResult<List<SpotTradeElementCurveListRespVo>> getCurve(SpotTradeElementCurveReqVo reqVo);

    ServiceResult<List<Map<String, Object>>> tradeReview(SpotTradeElementCurveReqVo reqVo);

    ServiceResult<List<SpotTradeElementCurveListRespVo>> tradeReviewCurve(SpotTradeElementCurveReqVo reqVo);
}
