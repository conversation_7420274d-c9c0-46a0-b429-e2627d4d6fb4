package com.bobandata.cloud.trade.elec.service.dayRolling;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveListRespVo;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.dayRolling.SpotTradeElementCurve;
import com.bobandata.cloud.trade.elec.dal.mysql.dayRolling.SpotTradeElementCurveMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service实现
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class SpotTradeElementCurveServiceImpl extends BaseServiceImpl<SpotTradeElementCurveMapper, SpotTradeElementCurve, Long> implements SpotTradeElementCurveService {


    @Autowired
    private SpotTradeElementCurveMapper spotTradeElementCurveMapper;

    @Override
    public ServiceResult<List<Map<String, Object>>> listRecord(SpotTradeElementCurveReqVo reqVo) {
        List<Map<String, Object>> list = this.getBaseMapper().listRecord(reqVo);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Map<String, Map<String, Object>> groupedByDate = new HashMap<>();
        Map<String, java.sql.Date> originalDateMap = new HashMap<>(); // 存储 java.sql.Date

        for (Map<String, Object> item : list) {
            Object curveDateObj = item.get("curve_date");
            String tradeType = (String) item.get("spot_trade_type");
            String curveType = (String) item.get("curve_type");
            Object calculatedValue = item.get("calculated_value");

            // 检查类型为 java.sql.Date
            if (curveDateObj instanceof java.sql.Date && tradeType != null && curveType != null) {
                java.sql.Date curveDate = (java.sql.Date) curveDateObj; // 转换为 java.sql.Date
                String curveDateKey = dateFormat.format(curveDate);

                originalDateMap.putIfAbsent(curveDateKey, curveDate);

                Map<String, Object> innerMap = groupedByDate.computeIfAbsent(curveDateKey, k -> new HashMap<>());
                String newKey = tradeType + curveType;
                innerMap.put(newKey, calculatedValue);
            }
        }

        List<Map<String, Object>> finalList = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : groupedByDate.entrySet()) {
            String curveDateKey = entry.getKey();
            Map<String, Object> tradeDataMap = entry.getValue();

            Map<String, Object> outputMap = new HashMap<>();
            outputMap.put("curve_date", originalDateMap.get(curveDateKey)); // 放入原始 java.sql.Date
            outputMap.putAll(tradeDataMap);

            finalList.add(outputMap);
        }

        // 3. 按 curve_date 排序

        finalList.sort(Comparator.comparing(m -> (java.sql.Date) m.get("curve_date"), Comparator.nullsFirst(Comparator.naturalOrder())));


        return ServiceResult.success(finalList);
    }

    @Override
    public ServiceResult<List<SpotTradeElementCurveListRespVo>> getCurve(SpotTradeElementCurveReqVo reqVo) {
        return ServiceResult.success(this.getBaseMapper().getCurve(reqVo));
    }

    @Override
    public ServiceResult<List<Map<String, Object>>> tradeReview(SpotTradeElementCurveReqVo reqVo) {
        List<Map<String, Object>> list = this.getBaseMapper().tradeReview(reqVo);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Map<String, Map<String, Object>> groupedByDate = new HashMap<>();
        Map<String, java.sql.Date> originalDateMap = new HashMap<>(); // 存储 java.sql.Date

        for (Map<String, Object> item : list) {
            Object curveDateObj = item.get("curve_date");
            String tradeType = (String) item.get("trade_type");
            String curveType = (String) item.get("curve_type");
            Object calculatedValue = item.get("calculated_value");

            // 检查类型为 java.sql.Date
            if (curveDateObj instanceof java.sql.Date && tradeType != null && curveType != null) {
                java.sql.Date curveDate = (java.sql.Date) curveDateObj; // 转换为 java.sql.Date
                String curveDateKey = dateFormat.format(curveDate);

                originalDateMap.putIfAbsent(curveDateKey, curveDate);

                Map<String, Object> innerMap = groupedByDate.computeIfAbsent(curveDateKey, k -> new HashMap<>());
                String newKey = tradeType + curveType;
                innerMap.put(newKey, calculatedValue);
            }
        }

        List<Map<String, Object>> finalList = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : groupedByDate.entrySet()) {
            String curveDateKey = entry.getKey();
            Map<String, Object> tradeDataMap = entry.getValue();

            Map<String, Object> outputMap = new HashMap<>();
            outputMap.put("curve_date", originalDateMap.get(curveDateKey)); // 放入原始 java.sql.Date
            outputMap.putAll(tradeDataMap);

            finalList.add(outputMap);
        }

        // 3. 按 curve_date 排序

        finalList.sort(Comparator.comparing(m -> (java.sql.Date) m.get("curve_date"), Comparator.nullsFirst(Comparator.naturalOrder())));
        return ServiceResult.success(finalList);
    }

    @Override
    public ServiceResult<List<SpotTradeElementCurveListRespVo>> tradeReviewCurve(SpotTradeElementCurveReqVo reqVo) {
        return ServiceResult.success(this.getBaseMapper().getTradeReviewCurve(reqVo));
    }


}




